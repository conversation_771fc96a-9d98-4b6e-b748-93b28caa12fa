import type { ColumnDef, FilterFn } from "@tanstack/react-table";
import { Package, EllipsisIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { AppRouter } from "../../../../../../server/src/routers";
import type { inferRouterOutputs } from "@trpc/server";

// Types
type RouterOutputs = inferRouterOutputs<AppRouter>;
type ProductsResponse = RouterOutputs["product"]["getAll"];
export type Product = ProductsResponse["products"][number];

// Custom filter function for multi-column searching
const multiColumnFilterFn: FilterFn<Product> = (row, columnId, filterValue) => {
  const searchableRowContent = `${row.original.name} ${
    row.original.description || ""
  } ${row.original.slug}`.toLowerCase();
  const searchTerm = (filterValue ?? "").toLowerCase();
  return searchableRowContent.includes(searchTerm);
};

// Row Actions Component
function RowActions({ row }: { row: any }) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="flex justify-end">
          <Button
            size="icon"
            variant="ghost"
            className="shadow-none"
            aria-label="Product actions"
          >
            <EllipsisIcon size={16} aria-hidden="true" />
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <span>View Details</span>
            <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <span>Edit Product</span>
            <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <span>Manage Variants</span>
            <DropdownMenuShortcut>⌘M</DropdownMenuShortcut>
          </DropdownMenuItem>
          <DropdownMenuItem>
            <span>Manage Stock</span>
            <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem>
            <span>Duplicate</span>
            <DropdownMenuShortcut>⌘D</DropdownMenuShortcut>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-destructive focus:text-destructive">
          <span>Delete Product</span>
          <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Column Definitions
export const columns: ColumnDef<Product>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    size: 28,
    enableSorting: false,
    enableHiding: false,
  },
  {
    header: "Product",
    accessorKey: "name",
    cell: ({ row }) => (
      <div className="flex items-center gap-3">
        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
          <Package className="h-4 w-4" />
        </div>
        <div>
          <div className="font-medium">{row.getValue("name")}</div>
          <div className="text-sm text-muted-foreground">
            /{row.original.slug}
          </div>
        </div>
      </div>
    ),
    size: 250,
    filterFn: multiColumnFilterFn,
    enableHiding: false,
  },
  {
    header: "Description",
    accessorKey: "description",
    cell: ({ row }) => (
      <div className="max-w-[200px] truncate">
        {row.getValue("description") || "—"}
      </div>
    ),
    size: 200,
  },
  {
    header: "Variants",
    accessorKey: "variants",
    cell: ({ row }) => {
      const variants = row.original.variants;
      return (
        <div className="flex items-center gap-2">
          <Badge variant="secondary">{variants.length}</Badge>
          {variants.length > 0 && (
            <span className="text-sm text-muted-foreground">
              $
              {Math.min(
                ...variants.map((v) => Number(v.price))
              ).toLocaleString()}{" "}
              - $
              {Math.max(
                ...variants.map((v) => Number(v.price))
              ).toLocaleString()}
            </span>
          )}
        </div>
      );
    },
    size: 150,
    enableSorting: false,
  },
  {
    header: "Stock",
    accessorKey: "stock",
    cell: ({ row }) => {
      const totalStock = row.original.variants.reduce(
        (acc: number, variant: any) => acc + variant.stock.length,
        0
      );
      const availableStock = row.original.variants.reduce(
        (acc: number, variant: any) =>
          acc + variant.stock.filter((s: any) => s.status === "AVAILABLE").length,
        0
      );

      return (
        <div className="flex items-center gap-2">
          <Badge
            variant={availableStock > 0 ? "default" : "secondary"}
            className={cn(
              availableStock === 0 &&
                "bg-muted-foreground/60 text-primary-foreground"
            )}
          >
            {availableStock}/{totalStock}
          </Badge>
          <span className="text-sm text-muted-foreground">available</span>
        </div>
      );
    },
    size: 120,
    enableSorting: false,
  },
  {
    header: "Created",
    accessorKey: "createdAt",
    cell: ({ row }) => {
      const date = row.getValue("createdAt") as Date;
      return <div className="text-sm">{date.toLocaleDateString()}</div>;
    },
    size: 100,
  },
  {
    id: "actions",
    header: () => <span className="sr-only">Actions</span>,
    cell: ({ row }) => <RowActions row={row} />,
    size: 60,
    enableHiding: false,
  },
];
