# Products Table Component

A comprehensive data table for managing products in the Bokul dashboard, built with TanStack Table and shadcn/ui components.

## Features

### ✅ **Implemented Features**

- **Server-side Pagination**: Efficient data loading with pagination controls
- **Real-time Search**: Search across product name, description, and slug
- **Column Sorting**: Sort by product name and creation date
- **Column Visibility**: Toggle column visibility with dropdown
- **Row Selection**: Multi-select rows with checkboxes
- **Responsive Design**: Mobile-friendly layout with horizontal scrolling
- **Loading States**: Detailed skeleton loading that matches actual table structure
- **Error Handling**: Comprehensive error display with retry functionality
- **Action Menu**: Per-row actions dropdown

### 📊 **Table Columns**

1. **Select**: Checkbox for row selection
2. **Product**: Product name with icon and slug
3. **Description**: Product description (truncated)
4. **Variants**: Number of variants with price range
5. **Stock**: Available/total stock with status badge
6. **Created**: Creation date
7. **Actions**: Dropdown menu with actions

### 🔍 **Search & Filtering**

- **Multi-column Search**: Searches across name, description, and slug
- **Debounced Search**: 500ms delay to prevent excessive API calls while typing
- **Clear Filter**: Easy filter reset button

### 📄 **Pagination**

- **Page Size Options**: 5, 10, 25, 50 items per page
- **Navigation Controls**: First, Previous, Next, Last page buttons
- **Results Counter**: Shows current range and total count
- **Server-side**: Efficient data loading

### 📱 **Responsive Design**

- **Custom ScrollArea**: Uses shadcn/ui ScrollArea component for consistent scrolling experience
- **Contained Horizontal Scrolling**: Table scrolls horizontally within its container only
- **Page Layout Stability**: Header, toolbar, and pagination remain fixed while table content scrolls
- **Proper Overflow Containment**: Horizontal scrollbar appears only within table area, not at page level
- **Minimum Width**: Table maintains 800px minimum width for readability
- **Flexible Search**: Search input adapts to screen size (200px min on mobile, 240px on desktop)
- **Touch-friendly**: Optimized for touch interactions on mobile devices
- **Isolated Scrolling**: Table scrolling doesn't affect overall page layout
- **Styled Scrollbars**: Custom-styled scrollbars that match the design system

### 🎛️ **Actions**

#### **Bulk Actions**

- **Delete Selected**: Delete multiple products (with confirmation)

#### **Row Actions**

- **View Details**: View product details
- **Edit Product**: Edit product information
- **Manage Variants**: Manage product variants
- **Manage Stock**: Manage stock items
- **Duplicate**: Create a copy of the product
- **Delete**: Delete the product

## Usage

```tsx
import ProductsTable from "./components/products-table";

function ProductsPage() {
  return (
    <div className="space-y-6">
      <PageHeader title="Products" description="Manage all your products." />
      <ProductsTable />
    </div>
  );
}
```

### **ScrollArea Implementation**

The table uses shadcn/ui's ScrollArea component for better scrolling experience:

```tsx
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";

// In table-content.tsx
<ScrollArea className="w-full">
  <TableComponent className="min-w-[800px]">
    {/* Table content */}
  </TableComponent>
  <ScrollBar orientation="horizontal" />
</ScrollArea>;
```

**Benefits:**

- Consistent scrollbar styling across browsers
- Better touch/mobile scrolling experience
- Customizable scrollbar appearance
- Proper focus management and accessibility

### **Enhanced Loading & Error States**

The table includes improved loading and error states that are fully responsive:

```tsx
// Loading state with accurate skeleton structure
<LoadingState />

// Error state with retry functionality
<ErrorState error={error} onRetry={() => refetch()} />
```

**Loading State Features:**

- Accurate skeleton that matches real table structure
- Responsive toolbar, table, and pagination skeletons
- Uses ScrollArea for consistent layout
- Proper column sizing that matches actual data

**Error State Features:**

- Clear error message display with technical details
- Retry button with refetch functionality
- Responsive layout that works on all screen sizes
- Disabled table skeleton to show expected layout
- Professional error card design

## API Integration

The table integrates with the tRPC product API:

```typescript
// Fetches products with pagination and search
const { data, isLoading, error } = trpc.product.getAll.useQuery({
  page: pagination.pageIndex + 1,
  limit: pagination.pageSize,
  search: searchValue || undefined,
});
```

### **API Response Format**

```typescript
{
  products: Product[],
  pagination: {
    page: number,
    limit: number,
    total: number,
    totalPages: number
  }
}
```

### **Product Type**

```typescript
type Product = {
  id: string;
  name: string;
  description?: string;
  slug: string;
  variants: Array<{
    id: string;
    name: string;
    price: number;
    stock: Array<{
      id: string;
      status: "AVAILABLE" | "ASSIGNED" | "SOLD";
    }>;
  }>;
  createdAt: string;
  updatedAt: string;
};
```

## Troubleshooting

### **tRPC Type Error**

If you encounter `Type 'never' has no call signatures` error:

1. **Check Router Export**: Ensure product router is properly exported in main router
2. **Verify tRPC Setup**: Check that tRPC client is configured correctly
3. **Type Generation**: Run `npm run build` to regenerate types
4. **Import Path**: Verify the import path for tRPC client

### **Common Solutions**

```typescript
// Make sure the router is properly exported
export const appRouter = router({
  product: productRouter,
  // ... other routers
});

// Verify tRPC client setup
import { trpc } from "@/utils/trpc";
```

## Performance

- **Server-side Pagination**: Only loads current page data
- **Debounced Search**: Prevents excessive API calls
- **Optimized Rendering**: TanStack Table's virtual scrolling ready
- **Memoized Calculations**: Efficient stock and variant calculations

## Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: Proper ARIA labels and descriptions
- **Focus Management**: Logical tab order
- **High Contrast**: Works with all theme modes

## Next Steps

1. **Implement CRUD Operations**: Add create, edit, delete functionality
2. **Advanced Filters**: Add status, date range, and category filters
3. **Export Functionality**: Add CSV/Excel export
4. **Bulk Operations**: Add bulk edit capabilities
5. **Real-time Updates**: Add WebSocket support for live updates
