import { useId, useState } from "react";
import {
  type ColumnFiltersState,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  type PaginationState,
  type SortingState,
  useReactTable,
  type VisibilityState,
} from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import { trpc } from "@/utils/trpc";
import { useDebounce } from "@/hooks/use-debounce";
import { LoadingState, ErrorState } from "./table-states";
import { TableToolbar } from "./table-toolbar";
import { TableContent } from "./table-content";
import { TablePagination } from "./table-pagination";
import { columns } from "./table-columns";

export default function ProductsTable() {
  const id = useId();
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: 10,
  });

  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "name",
      desc: false,
    },
  ]);

  // Get search filter value
  const searchValue =
    (columnFilters.find((f) => f.id === "name")?.value as string) || "";

  // Debounce the search value to prevent excessive API calls
  const debouncedSearchValue = useDebounce(searchValue, 500);

  // Fetch products using tRPC
  const {
    data: productsData,
    isLoading,
    error,
  } = useQuery(
    trpc.product.getAll.queryOptions({
      page: pagination.pageIndex + 1,
      limit: pagination.pageSize,
      search: debouncedSearchValue || undefined,
    })
  );

  const products = productsData?.products || [];
  const totalCount = productsData?.pagination.total || 0;

  const table = useReactTable({
    data: products,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    enableSortingRemoval: false,
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getFilteredRowModel: getFilteredRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pagination.pageSize),
    state: {
      sorting,
      pagination,
      columnFilters,
      columnVisibility,
    },
  });

  if (isLoading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState error={error} />;
  }

  return (
    <div className="space-y-4 w-full max-w-full overflow-hidden">
      <TableToolbar table={table} id={id} />
      <TableContent table={table} />
      <TablePagination table={table} totalCount={totalCount} id={id} />
    </div>
  );
}
