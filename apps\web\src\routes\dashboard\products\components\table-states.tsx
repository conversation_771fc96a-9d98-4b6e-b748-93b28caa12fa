export function LoadingState() {
  return (
    <div className="space-y-4">
      {/* Loading skeleton for filters */}
      <div className="flex flex-wrap items-center justify-between gap-3">
        <div className="flex items-center gap-3">
          <div className="h-10 w-60 animate-pulse rounded-md bg-muted" />
          <div className="h-10 w-20 animate-pulse rounded-md bg-muted" />
        </div>
        <div className="h-10 w-32 animate-pulse rounded-md bg-muted" />
      </div>

      {/* Loading skeleton for table */}
      <div className="bg-background overflow-hidden rounded-md border">
        <div className="p-4">
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4">
                <div className="h-4 w-4 animate-pulse rounded bg-muted" />
                <div className="h-4 w-32 animate-pulse rounded bg-muted" />
                <div className="h-4 w-24 animate-pulse rounded bg-muted" />
                <div className="h-4 w-16 animate-pulse rounded bg-muted" />
                <div className="h-4 w-20 animate-pulse rounded bg-muted" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

interface ErrorStateProps {
  error: { message: string };
}

export function ErrorState({ error }: ErrorStateProps) {
  return (
    <div className="flex h-32 items-center justify-center">
      <div className="text-destructive">
        Error loading products: {error.message}
      </div>
    </div>
  );
}
